-- =============================================
-- Author: System Generated
-- Create date: 2025-01-07
-- Description: Deployment Database - Get by Customer
-- =============================================
ALTER PROCEDURE [dbo].[sp_deployment_database_by_customer]
    @customerId UNIQUEIDENTIFIER,
    @productId UNIQUEIDENTIFIER = NULL
AS
BEGIN TRY
    -- Validation
    IF @customerId IS NULL
    BEGIN
        RAISERROR('Customer ID is required', 16, 1);
        RETURN;
    END;

    -- Get deployment databases by customer
    SELECT 
        dd.Id,
        dd.DeploymentId,
        dd.DatabaseId,
        dd.CustomerId,
        dd.ProductId,
        dd.DatabaseName,
        dd.ConnectionString,
        dd.Status,
        dd.Created,
        dd.CreatedBy,
        dd.Updated,
        dd.UpdatedBy
    FROM deployment_database dd
    WHERE dd.CustomerId = @customerId
    AND (@productId IS NULL OR dd.ProductId = @productId)
    AND dd.Status != 'DELETED'
    ORDER BY dd.Created DESC;

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_deployment_database_by_customer' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@customerId: ' + ISNULL(CAST(@customerId AS NVARCHAR(36)), 'NULL') + 
                   ', @productId: ' + ISNULL(CAST(@productId AS NVARCHAR(36)), 'NULL');

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'deployment_database',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;
GO
