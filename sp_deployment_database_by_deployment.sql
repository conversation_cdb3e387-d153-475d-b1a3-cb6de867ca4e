-- =============================================
-- Author: System Generated
-- Create date: 2025-01-07
-- Description: Deployment Database - Get by Deployment
-- =============================================
ALTER PROCEDURE [dbo].[sp_deployment_database_by_deployment]
    @deploymentId UNIQUEIDENTIFIER
AS
BEGIN TRY
    -- Validation
    IF @deploymentId IS NULL
    BEGIN
        RAISERROR('Deployment ID is required', 16, 1);
        RETURN;
    END;

    -- Get deployment databases by deployment
    SELECT 
        dd.Id,
        dd.DeploymentId,
        dd.DatabaseId,
        dd.CustomerId,
        dd.ProductId,
        dd.DatabaseName,
        dd.ConnectionString,
        dd.Status,
        dd.Created,
        dd.CreatedBy,
        dd.Updated,
        dd.UpdatedBy
    FROM deployment_database dd
    WHERE dd.DeploymentId = @deploymentId
    AND dd.Status != 'DELETED'
    ORDER BY dd.Created DESC;

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_deployment_database_by_deployment' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@deploymentId: ' + ISNULL(CAST(@deploymentId AS NVARCHAR(36)), 'NULL');

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'deployment_database',
                          'GET',
                          @SessionID,
                          @AddlInfo;
END CATCH;
GO
