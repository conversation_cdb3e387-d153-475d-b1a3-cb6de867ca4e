-- =============================================
-- Author: System Generated
-- Create date: 2025-01-07
-- Description: Deployment Database - Create New Database
-- =============================================
ALTER PROCEDURE [dbo].[sp_deployment_database_create]
    @CustomerId UNIQUEIDENTIFIER,
    @ProductId UNIQUEIDENTIFIER,
    @DeploymentId UNIQUEIDENTIFIER = NULL,
    @DatabaseName NVARCHAR(100),
    @DatabaseInstanceId UNIQUEIDENTIFIER = NULL,
    @SchemaVersionId UNIQUEIDENTIFIER = NULL,
    @Notes NVARCHAR(500) = NULL,
    @userId NVARCHAR(450) = NULL
AS
BEGIN TRY
    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250),
            @newId UNIQUEIDENTIFIER;

    -- Validation
    IF @CustomerId IS NULL
    BEGIN
        SET @messages = N'Customer ID is required';
        GOTO FINAL;
    END;

    IF @ProductId IS NULL
    BEGIN
        SET @messages = N'Product ID is required';
        GOTO FINAL;
    END;

    IF @DatabaseName IS NULL OR LTRIM(RTRIM(@DatabaseName)) = ''
    BEGIN
        SET @messages = N'Database name is required';
        GOTO FINAL;
    END;

    -- Check if customer exists
    IF NOT EXISTS (SELECT 1 FROM customer WHERE Id = @CustomerId)
    BEGIN
        SET @messages = N'Customer not found';
        GOTO FINAL;
    END;

    -- Check if product exists
    IF NOT EXISTS (SELECT 1 FROM product WHERE Id = @ProductId)
    BEGIN
        SET @messages = N'Product not found';
        GOTO FINAL;
    END;

    -- Check if deployment exists (if provided)
    IF @DeploymentId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM deployment WHERE Id = @DeploymentId)
    BEGIN
        SET @messages = N'Deployment not found';
        GOTO FINAL;
    END;

    -- Check if database name already exists for this customer/product
    IF EXISTS (
        SELECT 1 
        FROM deployment_database 
        WHERE CustomerId = @CustomerId 
        AND ProductId = @ProductId 
        AND DatabaseName = @DatabaseName
        AND Status != 'DELETED'
    )
    BEGIN
        SET @messages = N'Database name already exists for this customer and product';
        GOTO FINAL;
    END;

    -- Generate new ID
    SET @newId = NEWID();

    -- Create database record
    INSERT INTO deployment_database
    (
        Id,
        DeploymentId,
        DatabaseId,
        CustomerId,
        ProductId,
        DatabaseName,
        ConnectionString,
        Status,
        SchemaVersionId,
        DatabaseInstanceId,
        Notes,
        Created,
        CreatedBy
    )
    VALUES
    (   
        @newId,
        @DeploymentId,
        NULL, -- Will be set after actual database creation
        @CustomerId,
        @ProductId,
        @DatabaseName,
        NULL, -- Will be generated after database creation
        'CREATING',
        @SchemaVersionId,
        @DatabaseInstanceId,
        @Notes,
        GETDATE(),
        @userId
    );

    SET @valid = 1;
    SET @messages = N'Deployment database creation initiated successfully';

    FINAL:
    SELECT @valid valid,
           @messages AS [messages],
           @newId AS Data;

END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_deployment_database_create' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@CustomerId: ' + ISNULL(CAST(@CustomerId AS NVARCHAR(36)), 'NULL') + 
                   ', @ProductId: ' + ISNULL(CAST(@ProductId AS NVARCHAR(36)), 'NULL') +
                   ', @DatabaseName: ' + ISNULL(@DatabaseName, 'NULL');

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'deployment_database',
                          'CREATE',
                          @SessionID,
                          @AddlInfo;
END CATCH;
GO
