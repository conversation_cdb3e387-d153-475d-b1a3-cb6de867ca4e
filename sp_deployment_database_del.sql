-- =============================================
-- Author: System Generated
-- Create date: 2025-01-07
-- Description: Deployment Database - Delete
-- =============================================
ALTER PROCEDURE [dbo].[sp_deployment_database_del]
    @userId NVARCHAR(50),
    @id UNIQUEIDENTIFIER
AS
BEGIN TRY
    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    -- Validation
    IF @id IS NULL
    BEGIN
        SET @messages = N'Database ID is required';
        GOTO FINAL;
    END;

    IF NOT EXISTS (SELECT 1 FROM deployment_database WHERE Id = @id)
    BEGIN
        SET @messages = N'Deployment database not found';
        GOTO FINAL;
    END;

    -- Check if database is in use by any active deployments
    IF EXISTS (
        SELECT 1 
        FROM deployment_database dd
        INNER JOIN deployment d ON dd.DeploymentId = d.Id
        WHERE dd.Id = @id 
        AND d.Status IN (1, 2) -- Active or In Progress statuses
    )
    BEGIN
        SET @messages = N'Cannot delete database that is part of an active deployment';
        GOTO FINAL;
    END;

    -- Check if database has any dependent records (if applicable)
    -- Add additional checks here based on business rules

    -- Soft delete by updating status
    UPDATE deployment_database
    SET 
        Status = 'DELETED',
        Updated = GETDATE(),
        UpdatedBy = @userId
    WHERE Id = @id;

    -- Or hard delete if preferred
    -- DELETE FROM deployment_database WHERE Id = @id;

    SET @valid = 1;
    SET @messages = N'Deployment database deleted successfully';

    FINAL:
    SELECT @valid valid,
           @messages AS [messages];

END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_deployment_database_del' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@UserId: ' + ISNULL(@userId, 'NULL') + ', @Id: ' + ISNULL(CAST(@id AS NVARCHAR(36)), 'NULL');

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'deployment_database',
                          'DEL',
                          @SessionID,
                          @AddlInfo;
END CATCH;
GO
