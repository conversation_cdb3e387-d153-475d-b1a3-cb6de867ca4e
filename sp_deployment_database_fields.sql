-- =============================================
-- Author: System Generated
-- Create date: 2025-01-07
-- Description: Deployment Database - Get Fields/Info
-- =============================================
ALTER PROCEDURE [dbo].[sp_deployment_database_fields]
    @id UNIQUEIDENTIFIER = NULL,
    @acceptLanguage NVARCHAR(50) = 'vi-VN'
AS
BEGIN TRY
    DECLARE @tableKey VARCHAR(50) = 'deployment_database'
    DECLARE @groupKey VARCHAR(50) = 'mas_deployment_database_group_update'

    -- Return basic info
    SELECT @id oid
        , tableKey = @tableKey
        , groupKey = @groupKey

    -- Return field groups
    SELECT *
    FROM [dbo].[fn_get_field_group](@groupKey, @acceptLanguage)
    ORDER BY intOrder

    -- Get deployment database data
    SELECT 
        dd.Id,
        dd.DeploymentId,
        dd.DatabaseId,
        dd.CustomerId,
        dd.ProductId,
        dd.DatabaseName,
        dd.ConnectionString,
        dd.Status,
        dd.Created,
        dd.CreatedBy,
        dd.Updated,
        dd.UpdatedBy,
        dd.SchemaVersionId,
        dd.Notes,
        c.Name as CustomerName,
        p.Name as ProductName,
        dep.Name as DeploymentName,
        di.Name as InstanceName,
        dv.Name as SchemaVersion,
        dv.RefUrl as SchemaVersionUrl,
        db.Name as DatabaseInstanceName,
        db.UserName as DatabaseUserName,
        db.Password as DatabasePassword,
        db.NumOfTenant
    INTO #deployment_database
    FROM deployment_database dd
    LEFT JOIN customer c ON dd.CustomerId = c.Id
    LEFT JOIN product p ON dd.ProductId = p.Id
    LEFT JOIN deployment dep ON dd.DeploymentId = dep.Id
    LEFT JOIN database_instance di ON dd.DatabaseInstanceId = di.Id
    LEFT JOIN dbversion dv ON dd.SchemaVersionId = dv.Id
    LEFT JOIN [database] db ON dd.DatabaseId = db.Id
    WHERE dd.Id = @id

    -- Execute config data fields
    EXEC sp_config_data_fields 
        @id = @id,
        @tableName = @tableKey,
        @dataTableName = '#deployment_database',
        @acceptLanguage = @acceptLanguage

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_deployment_database_fields' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = ' ';

    EXEC utl_ErrorLog_Set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , @tableKey
        , 'GET'
        , @SessionID
        , @AddlInfo;
END CATCH;
GO
