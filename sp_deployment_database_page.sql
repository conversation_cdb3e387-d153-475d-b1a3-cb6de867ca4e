-- =============================================
-- Author:		System Generated
-- Create date: 2025-01-07
-- Description:	Deployment Database - Get Page
-- =============================================
ALTER PROCEDURE [dbo].[sp_deployment_database_page] 
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @ProductId UNIQUEIDENTIFIER = NULL,
    @DeploymentId UNIQUEIDENTIFIER = NULL,
    @Status NVARCHAR(50) = NULL,
    @CreatedFrom DATETIME = NULL,
    @CreatedTo DATETIME = NULL,
    @filter NVARCHAR(100) = NULL,
    @Offset INT = 0,
    @PageSize INT = 10,
    @gridWidth INT = 0,
    @gridKey NVARCHAR(100) = NULL OUT,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT,
    @acceptLanguage NVARCHAR(50) = 'vi-VN'
AS
BEGIN TRY
    SET @gridKey = 'deployment_database_page'
    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');

    -- Validate parameters
    IF @PageSize <= 0
        SET @PageSize = 10;

    IF @Offset < 0
        SET @Offset = 0;

    -- Build WHERE clause
    DECLARE @whereClause NVARCHAR(MAX) = ' WHERE 1=1 ';
    
    IF @CustomerId IS NOT NULL
        SET @whereClause = @whereClause + ' AND dd.CustomerId = ''' + CAST(@CustomerId AS NVARCHAR(36)) + '''';
    
    IF @ProductId IS NOT NULL
        SET @whereClause = @whereClause + ' AND dd.ProductId = ''' + CAST(@ProductId AS NVARCHAR(36)) + '''';
    
    IF @DeploymentId IS NOT NULL
        SET @whereClause = @whereClause + ' AND dd.DeploymentId = ''' + CAST(@DeploymentId AS NVARCHAR(36)) + '''';
    
    IF @Status IS NOT NULL AND @Status != ''
        SET @whereClause = @whereClause + ' AND dd.Status = ''' + @Status + '''';
    
    IF @CreatedFrom IS NOT NULL
        SET @whereClause = @whereClause + ' AND dd.Created >= ''' + CONVERT(NVARCHAR(23), @CreatedFrom, 121) + '''';
    
    IF @CreatedTo IS NOT NULL
        SET @whereClause = @whereClause + ' AND dd.Created <= ''' + CONVERT(NVARCHAR(23), @CreatedTo, 121) + '''';
    
    IF @filter IS NOT NULL AND @filter != ''
        SET @whereClause = @whereClause + ' AND (dd.DatabaseName LIKE ''%' + @filter + '%'' OR c.Name LIKE ''%' + @filter + '%'' OR p.Name LIKE ''%' + @filter + '%'')';

    -- Get total count
    DECLARE @countSql NVARCHAR(MAX) = '
        SELECT @Total = COUNT(1)
        FROM deployment_database dd
        LEFT JOIN customer c ON dd.CustomerId = c.Id
        LEFT JOIN product p ON dd.ProductId = p.Id
        LEFT JOIN deployment dep ON dd.DeploymentId = dep.Id
        ' + @whereClause;

    EXEC sp_executesql @countSql, N'@Total INT OUTPUT', @Total OUTPUT;
    SET @TotalFiltered = @Total;

    -- Return grid configuration if first page
    IF @Offset = 0
    BEGIN
        SELECT *
        FROM [dbo].fn_config_list_gets(@gridKey, 0, @acceptLanguage)
        ORDER BY [ordinal];
    END;

    -- Get data with pagination
    DECLARE @dataSql NVARCHAR(MAX) = '
        SELECT 
            dd.Id,
            dd.DeploymentId,
            dd.DatabaseId,
            dd.CustomerId,
            dd.ProductId,
            dd.DatabaseName,
            dd.ConnectionString,
            dd.Status,
            dd.Created,
            dd.CreatedBy,
            dd.Updated,
            dd.UpdatedBy,
            c.Name as CustomerName,
            p.Name as ProductName,
            dep.Name as DeploymentName,
            di.Name as InstanceName,
            dv.Name as SchemaVersion,
            ROW_NUMBER() OVER (ORDER BY dd.Created DESC) AS stt
        FROM deployment_database dd
        LEFT JOIN customer c ON dd.CustomerId = c.Id
        LEFT JOIN product p ON dd.ProductId = p.Id
        LEFT JOIN deployment dep ON dd.DeploymentId = dep.Id
        LEFT JOIN database_instance di ON dd.DatabaseId = di.Id
        LEFT JOIN dbversion dv ON dd.SchemaVersionId = dv.Id
        ' + @whereClause + '
        ORDER BY dd.Created DESC 
        OFFSET ' + CAST(@Offset AS NVARCHAR(10)) + ' ROWS
        FETCH NEXT ' + CAST(@PageSize AS NVARCHAR(10)) + ' ROWS ONLY';

    EXEC sp_executesql @dataSql;

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT
        , @ErrorMsg VARCHAR(200)
        , @ErrorProc VARCHAR(50)
        , @SessionID INT
        , @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = '';

    EXEC utl_errorlog_set @ErrorNum
        , @ErrorMsg
        , @ErrorProc
        , 'deployment_database'
        , 'GET'
        , @SessionID
        , @AddlInfo;
END CATCH
GO
