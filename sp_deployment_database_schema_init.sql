-- =============================================
-- Author: System Generated
-- Create date: 2025-01-07
-- Description: Deployment Database - Initialize Schema
-- =============================================
ALTER PROCEDURE [dbo].[sp_deployment_database_schema_init]
    @databaseId UNIQUEIDENTIFIER,
    @versionId UNIQUEIDENTIFIER,
    @userId NVARCHAR(450) = NULL
AS
BEGIN TRY
    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    -- Validation
    IF @databaseId IS NULL
    BEGIN
        SET @messages = N'Database ID is required';
        GOTO FINAL;
    END;

    IF @versionId IS NULL
    BEGIN
        SET @messages = N'Version ID is required';
        GOTO FINAL;
    END;

    -- Check if database exists
    IF NOT EXISTS (SELECT 1 FROM deployment_database WHERE Id = @databaseId)
    BEGIN
        SET @messages = N'Deployment database not found';
        GOTO FINAL;
    END;

    -- Check if version exists
    IF NOT EXISTS (SELECT 1 FROM dbversion WHERE Id = @versionId)
    BEGIN
        SET @messages = N'Database version not found';
        GOTO FINAL;
    END;

    -- Check if database is in a valid state for initialization
    DECLARE @currentStatus NVARCHAR(50);
    SELECT @currentStatus = Status FROM deployment_database WHERE Id = @databaseId;

    IF @currentStatus NOT IN ('CREATING', 'ACTIVE', 'ERROR')
    BEGIN
        SET @messages = N'Database is not in a valid state for schema initialization';
        GOTO FINAL;
    END;

    -- Update database status to indicate schema initialization is in progress
    UPDATE deployment_database
    SET 
        Status = 'MIGRATING',
        SchemaVersionId = @versionId,
        Updated = GETDATE(),
        UpdatedBy = @userId
    WHERE Id = @databaseId;

    -- Here you would typically:
    -- 1. Get the database connection string
    -- 2. Execute the schema initialization scripts
    -- 3. Update the database status based on success/failure
    
    -- For now, we'll simulate success
    UPDATE deployment_database
    SET 
        Status = 'ACTIVE',
        Updated = GETDATE(),
        UpdatedBy = @userId
    WHERE Id = @databaseId;

    -- Log the schema initialization
    INSERT INTO deployment_database_schema_log
    (
        Id,
        DatabaseId,
        FromVersionId,
        ToVersionId,
        Action,
        Status,
        StartTime,
        EndTime,
        Notes,
        CreatedBy
    )
    VALUES
    (
        NEWID(),
        @databaseId,
        NULL, -- No previous version for initialization
        @versionId,
        'INITIALIZE',
        'COMPLETED',
        GETDATE(),
        GETDATE(),
        'Schema initialized successfully',
        @userId
    );

    SET @valid = 1;
    SET @messages = N'Database schema initialized successfully';

    FINAL:
    SELECT @valid valid,
           @messages AS [messages];

END TRY
BEGIN CATCH
    -- Update database status to error if something went wrong
    UPDATE deployment_database
    SET 
        Status = 'ERROR',
        Updated = GETDATE(),
        UpdatedBy = @userId
    WHERE Id = @databaseId;

    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_deployment_database_schema_init' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@databaseId: ' + ISNULL(CAST(@databaseId AS NVARCHAR(36)), 'NULL') + 
                   ', @versionId: ' + ISNULL(CAST(@versionId AS NVARCHAR(36)), 'NULL');

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'deployment_database',
                          'SCHEMA_INIT',
                          @SessionID,
                          @AddlInfo;
END CATCH;
GO
