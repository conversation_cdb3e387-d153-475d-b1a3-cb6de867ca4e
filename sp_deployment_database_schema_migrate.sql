-- =============================================
-- Author: System Generated
-- Create date: 2025-01-07
-- Description: Deployment Database - Migrate Schema
-- =============================================
ALTER PROCEDURE [dbo].[sp_deployment_database_schema_migrate]
    @DatabaseId UNIQUEIDENTIFIER,
    @FromVersionId UNIQUEIDENTIFIER,
    @ToVersionId UNIQUEIDENTIFIER,
    @BackupBeforeMigration BIT = 1,
    @Notes NVARCHAR(500) = NULL,
    @userId NVARCHAR(450) = NULL
AS
BEGIN TRY
    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250),
            @migrationLogId UNIQUEIDENTIFIER;

    -- Validation
    IF @DatabaseId IS NULL
    BEGIN
        SET @messages = N'Database ID is required';
        GOTO FINAL;
    END;

    IF @FromVersionId IS NULL
    BEGIN
        SET @messages = N'From Version ID is required';
        GOTO FINAL;
    END;

    IF @ToVersionId IS NULL
    BEGIN
        SET @messages = N'To Version ID is required';
        GOTO FINAL;
    END;

    -- Check if database exists
    IF NOT EXISTS (SELECT 1 FROM deployment_database WHERE Id = @DatabaseId)
    BEGIN
        SET @messages = N'Deployment database not found';
        GOTO FINAL;
    END;

    -- Check if versions exist
    IF NOT EXISTS (SELECT 1 FROM dbversion WHERE Id = @FromVersionId)
    BEGIN
        SET @messages = N'From database version not found';
        GOTO FINAL;
    END;

    IF NOT EXISTS (SELECT 1 FROM dbversion WHERE Id = @ToVersionId)
    BEGIN
        SET @messages = N'To database version not found';
        GOTO FINAL;
    END;

    -- Check if database is in a valid state for migration
    DECLARE @currentStatus NVARCHAR(50), @currentVersionId UNIQUEIDENTIFIER;
    SELECT @currentStatus = Status, @currentVersionId = SchemaVersionId 
    FROM deployment_database WHERE Id = @DatabaseId;

    IF @currentStatus NOT IN ('ACTIVE', 'ERROR')
    BEGIN
        SET @messages = N'Database is not in a valid state for schema migration';
        GOTO FINAL;
    END;

    -- Verify current version matches from version
    IF @currentVersionId != @FromVersionId
    BEGIN
        SET @messages = N'Current database version does not match the specified from version';
        GOTO FINAL;
    END;

    -- Create migration log entry
    SET @migrationLogId = NEWID();
    INSERT INTO deployment_database_schema_log
    (
        Id,
        DatabaseId,
        FromVersionId,
        ToVersionId,
        Action,
        Status,
        StartTime,
        Notes,
        CreatedBy
    )
    VALUES
    (
        @migrationLogId,
        @DatabaseId,
        @FromVersionId,
        @ToVersionId,
        'MIGRATE',
        'IN_PROGRESS',
        GETDATE(),
        @Notes,
        @userId
    );

    -- Update database status to indicate migration is in progress
    UPDATE deployment_database
    SET 
        Status = 'MIGRATING',
        Updated = GETDATE(),
        UpdatedBy = @userId
    WHERE Id = @DatabaseId;

    -- Here you would typically:
    -- 1. Create backup if requested
    -- 2. Get the database connection string
    -- 3. Execute the migration scripts from FromVersion to ToVersion
    -- 4. Update the database status based on success/failure
    
    -- Simulate backup creation if requested
    IF @BackupBeforeMigration = 1
    BEGIN
        -- Log backup creation (in real implementation, create actual backup)
        INSERT INTO deployment_database_backup_log
        (
            Id,
            DatabaseId,
            BackupName,
            BackupPath,
            BackupSize,
            Status,
            Created,
            CreatedBy
        )
        VALUES
        (
            NEWID(),
            @DatabaseId,
            'pre_migration_' + FORMAT(GETDATE(), 'yyyyMMdd_HHmmss'),
            '/backups/pre_migration_' + CAST(@DatabaseId AS NVARCHAR(36)) + '_' + FORMAT(GETDATE(), 'yyyyMMdd_HHmmss') + '.bak',
            0, -- Size would be populated after actual backup
            'COMPLETED',
            GETDATE(),
            @userId
        );
    END;

    -- For now, we'll simulate successful migration
    UPDATE deployment_database
    SET 
        Status = 'ACTIVE',
        SchemaVersionId = @ToVersionId,
        Updated = GETDATE(),
        UpdatedBy = @userId
    WHERE Id = @DatabaseId;

    -- Update migration log
    UPDATE deployment_database_schema_log
    SET 
        Status = 'COMPLETED',
        EndTime = GETDATE(),
        Notes = ISNULL(@Notes, '') + ' - Migration completed successfully'
    WHERE Id = @migrationLogId;

    SET @valid = 1;
    SET @messages = N'Database schema migrated successfully';

    FINAL:
    SELECT @valid valid,
           @messages AS [messages];

END TRY
BEGIN CATCH
    -- Update database status to error if something went wrong
    UPDATE deployment_database
    SET 
        Status = 'ERROR',
        Updated = GETDATE(),
        UpdatedBy = @userId
    WHERE Id = @DatabaseId;

    -- Update migration log
    IF @migrationLogId IS NOT NULL
    BEGIN
        UPDATE deployment_database_schema_log
        SET 
            Status = 'FAILED',
            EndTime = GETDATE(),
            Notes = ISNULL(@Notes, '') + ' - Migration failed: ' + ERROR_MESSAGE()
        WHERE Id = @migrationLogId;
    END;

    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_deployment_database_schema_migrate' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@DatabaseId: ' + ISNULL(CAST(@DatabaseId AS NVARCHAR(36)), 'NULL') + 
                   ', @FromVersionId: ' + ISNULL(CAST(@FromVersionId AS NVARCHAR(36)), 'NULL') +
                   ', @ToVersionId: ' + ISNULL(CAST(@ToVersionId AS NVARCHAR(36)), 'NULL');

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'deployment_database',
                          'SCHEMA_MIGRATE',
                          @SessionID,
                          @AddlInfo;
END CATCH;
GO
