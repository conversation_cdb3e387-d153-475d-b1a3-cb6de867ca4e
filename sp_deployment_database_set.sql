-- =============================================
-- Author: System Generated
-- Create date: 2025-01-07
-- Description: Deployment Database - Create/Update
-- =============================================
ALTER PROCEDURE [dbo].[sp_deployment_database_set]
    @userId NVARCHAR(450) = NULL,
    @Id UNIQUEIDENTIFIER = NULL,
    @DeploymentId UNIQUEIDENTIFIER = NULL,
    @DatabaseId UNIQUEIDENTIFIER = NULL,
    @CustomerId UNIQUEIDENTIFIER = NULL,
    @ProductId UNIQUEIDENTIFIER = NULL,
    @DatabaseName NVARCHAR(100) = NULL,
    @ConnectionString NVARCHAR(500) = NULL,
    @Status NVARCHAR(50) = NULL,
    @SchemaVersionId UNIQUEIDENTIFIER = NULL,
    @DatabaseInstanceId UNIQUEIDENTIFIER = NULL,
    @Notes NVARCHAR(500) = NULL
AS
BEGIN TRY
    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    -- Validation
    IF @CustomerId IS NULL
    BEGIN
        SET @messages = N'Customer ID is required';
        GOTO FINAL;
    END;

    IF @ProductId IS NULL
    BEGIN
        SET @messages = N'Product ID is required';
        GOTO FINAL;
    END;

    IF @DatabaseName IS NULL OR LTRIM(RTRIM(@DatabaseName)) = ''
    BEGIN
        SET @messages = N'Database name is required';
        GOTO FINAL;
    END;

    -- Check if customer exists
    IF NOT EXISTS (SELECT 1 FROM customer WHERE Id = @CustomerId)
    BEGIN
        SET @messages = N'Customer not found';
        GOTO FINAL;
    END;

    -- Check if product exists
    IF NOT EXISTS (SELECT 1 FROM product WHERE Id = @ProductId)
    BEGIN
        SET @messages = N'Product not found';
        GOTO FINAL;
    END;

    -- Check if deployment exists (if provided)
    IF @DeploymentId IS NOT NULL AND NOT EXISTS (SELECT 1 FROM deployment WHERE Id = @DeploymentId)
    BEGIN
        SET @messages = N'Deployment not found';
        GOTO FINAL;
    END;

    IF (@Id IS NULL)
    BEGIN
        -- INSERT operation
        SET @Id = NEWID();
        SET @Status = ISNULL(@Status, 'CREATING');

        INSERT INTO deployment_database
        (
            Id,
            DeploymentId,
            DatabaseId,
            CustomerId,
            ProductId,
            DatabaseName,
            ConnectionString,
            Status,
            SchemaVersionId,
            DatabaseInstanceId,
            Notes,
            Created,
            CreatedBy
        )
        VALUES
        (   
            @Id,
            @DeploymentId,
            @DatabaseId,
            @CustomerId,
            @ProductId,
            @DatabaseName,
            @ConnectionString,
            @Status,
            @SchemaVersionId,
            @DatabaseInstanceId,
            @Notes,
            GETDATE(),
            @userId
        );

        SET @valid = 1;
        SET @messages = N'Deployment database created successfully';
    END;
    ELSE
    BEGIN
        -- UPDATE operation
        IF NOT EXISTS (SELECT 1 FROM deployment_database WHERE Id = @Id)
        BEGIN
            SET @messages = N'Deployment database not found';
            GOTO FINAL;
        END;

        UPDATE deployment_database
        SET 
            DeploymentId = ISNULL(@DeploymentId, DeploymentId),
            DatabaseId = ISNULL(@DatabaseId, DatabaseId),
            CustomerId = ISNULL(@CustomerId, CustomerId),
            ProductId = ISNULL(@ProductId, ProductId),
            DatabaseName = ISNULL(@DatabaseName, DatabaseName),
            ConnectionString = ISNULL(@ConnectionString, ConnectionString),
            Status = ISNULL(@Status, Status),
            SchemaVersionId = @SchemaVersionId,
            DatabaseInstanceId = @DatabaseInstanceId,
            Notes = @Notes,
            Updated = GETDATE(),
            UpdatedBy = @userId
        WHERE Id = @Id;

        SET @valid = 1;
        SET @messages = N'Deployment database updated successfully';
    END;

    FINAL:
    SELECT @valid valid,
           @messages AS [messages],
           @Id AS Id;

END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_deployment_database_set' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@UserId: ' + ISNULL(@userId, 'NULL') + ', @Id: ' + ISNULL(CAST(@Id AS NVARCHAR(36)), 'NULL');

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'deployment_database',
                          'Set',
                          @SessionID,
                          @AddlInfo;
END CATCH;
GO
