-- =============================================
-- Author: System Generated
-- Create date: 2025-01-07
-- Description: Deployment Database - Update Existing Database
-- =============================================
ALTER PROCEDURE [dbo].[sp_deployment_database_update]
    @Id UNIQUEIDENTIFIER,
    @DatabaseName NVARCHAR(100) = NULL,
    @ConnectionString NVARCHAR(500) = NULL,
    @Status NVARCHAR(50) = NULL,
    @SchemaVersionId UNIQUEIDENTIFIER = NULL,
    @Notes NVARCHAR(500) = NULL,
    @userId NVARCHAR(450) = NULL
AS
BEGIN TRY
    DECLARE @valid BIT = 0,
            @messages NVARCHAR(250);

    -- Validation
    IF @Id IS NULL
    BEGIN
        SET @messages = N'Database ID is required';
        GOTO FINAL;
    END;

    IF NOT EXISTS (SELECT 1 FROM deployment_database WHERE Id = @Id)
    BEGIN
        SET @messages = N'Deployment database not found';
        GOTO FINAL;
    END;

    -- Check if database is in a state that allows updates
    DECLARE @currentStatus NVARCHAR(50);
    SELECT @currentStatus = Status FROM deployment_database WHERE Id = @Id;

    IF @currentStatus = 'DELETED'
    BEGIN
        SET @messages = N'Cannot update deleted database';
        GOTO FINAL;
    END;

    -- If updating database name, check for duplicates
    IF @DatabaseName IS NOT NULL
    BEGIN
        DECLARE @customerId UNIQUEIDENTIFIER, @productId UNIQUEIDENTIFIER;
        SELECT @customerId = CustomerId, @productId = ProductId 
        FROM deployment_database WHERE Id = @Id;

        IF EXISTS (
            SELECT 1 
            FROM deployment_database 
            WHERE CustomerId = @customerId 
            AND ProductId = @productId 
            AND DatabaseName = @DatabaseName
            AND Id != @Id
            AND Status != 'DELETED'
        )
        BEGIN
            SET @messages = N'Database name already exists for this customer and product';
            GOTO FINAL;
        END;
    END;

    -- Update the database record
    UPDATE deployment_database
    SET 
        DatabaseName = ISNULL(@DatabaseName, DatabaseName),
        ConnectionString = ISNULL(@ConnectionString, ConnectionString),
        Status = ISNULL(@Status, Status),
        SchemaVersionId = ISNULL(@SchemaVersionId, SchemaVersionId),
        Notes = ISNULL(@Notes, Notes),
        Updated = GETDATE(),
        UpdatedBy = @userId
    WHERE Id = @Id;

    SET @valid = 1;
    SET @messages = N'Deployment database updated successfully';

    FINAL:
    SELECT @valid valid,
           @messages AS [messages];

END TRY
BEGIN CATCH
    SELECT @messages AS [messages];
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(200),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_deployment_database_update' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();

    SET @AddlInfo = '@Id: ' + ISNULL(CAST(@Id AS NVARCHAR(36)), 'NULL') + 
                   ', @DatabaseName: ' + ISNULL(@DatabaseName, 'NULL') +
                   ', @Status: ' + ISNULL(@Status, 'NULL');

    EXEC utl_ErrorLog_Set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'deployment_database',
                          'UPDATE',
                          @SessionID,
                          @AddlInfo;
END CATCH;
GO
